import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { CustomerContractForm } from '../components/contract';
import { CustomerContract } from '../models';
import { contractService } from '../services/contract/contractService';
import { LoadingSpinner, ErrorAlert, SuccessAlert } from '../components/common';
import { calculateContractAmount } from '../utils/contractCalculationUtils';

const CreateContractPage: React.FC = () => {
  const navigate = useNavigate();
  const [contract, setContract] = useState<Partial<CustomerContract>>({
    customerId: 0,
    startingDate: '',
    endingDate: '',
    totalAmount: 0,
    description: '',
    jobDetails: [],
    status: 0 // Pending
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {
    setContract(updatedContract);
  };

  const validateContract = (): boolean => {
    if (!contract.customerId || contract.customerId === 0) {
      setError('Vui lòng chọn khách hàng');
      return false;
    }

    // Contract dates are auto-calculated from job details, no need to validate manually
    // Contract address is auto-derived from job details, no need to validate manually
    // Total amount is auto-calculated, no need to validate manually

    if (!contract.jobDetails || contract.jobDetails.length === 0) {
      setError('Vui lòng thêm ít nhất một chi tiết công việc');
      return false;
    }

    // Validate each job detail
    for (const jobDetail of contract.jobDetails) {
      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {
        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.startDate) {
        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');
        return false;
      }

      if (!jobDetail.endDate) {
        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');
        return false;
      }

      // Work location is optional - will be auto-assigned if not provided
      // if (!jobDetail.workLocation) {
      //   setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');
      //   return false;
      // }

      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {
        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');
        return false;
      }

      // Validate each work shift
      for (const workShift of jobDetail.workShifts) {
        if (!workShift.startTime) {
          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.endTime) {
          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {
          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');
          return false;
        }

        if (workShift.salary === undefined || workShift.salary < 0) {
          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');
          return false;
        }

        if (!workShift.workingDays) {
          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    // Prevent double submission with multiple checks
    if (loading) {
      console.log('Contract submission blocked: already loading');
      return;
    }

    // Enhanced duplicate prevention
    const now = Date.now();
    const lastSubmission = localStorage.getItem('lastContractSubmission');
    const submissionKey = `contract_${contract.customerId}_${contract.startingDate}_${contract.endingDate}_${Math.round(contract.totalAmount || 0)}`;
    const lastSubmissionKey = localStorage.getItem('lastContractSubmissionKey');

    // Prevent rapid successive submissions
    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {
      console.log('Contract submission blocked: too rapid (within 2 seconds)');
      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');
      return;
    }

    // Prevent duplicate contract submissions
    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {
      console.log('Contract submission blocked: duplicate contract detected');
      setError('Hợp đồng tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');
      return;
    }

    setError(null);

    // Debug: Log contract data before validation
    console.log('🔍 DEBUG: Contract data before validation:', {
      customerId: contract.customerId,
      startingDate: contract.startingDate,
      endingDate: contract.endingDate,
      totalAmount: contract.totalAmount,
      description: contract.description,
      jobDetailsCount: contract.jobDetails?.length || 0,
      jobDetails: contract.jobDetails?.map((jd, index) => ({
        index,
        jobCategoryId: jd.jobCategoryId,
        startDate: jd.startDate,
        endDate: jd.endDate,
        workLocation: jd.workLocation,
        workShiftsCount: jd.workShifts?.length || 0,
        workShifts: jd.workShifts?.map((ws, wsIndex) => ({
          wsIndex,
          startTime: ws.startTime,
          endTime: ws.endTime,
          numberOfWorkers: ws.numberOfWorkers,
          salary: ws.salary,
          workingDays: ws.workingDays
        }))
      }))
    });

    if (!validateContract()) {
      console.log('❌ Contract validation failed');
      return;
    }

    console.log('✅ Contract validation passed');

    // Ensure total amount is calculated before submitting
    const calculation = calculateContractAmount(contract);

    // Create clean contract object without address field - explicitly exclude address
    const contractToSubmit: Partial<CustomerContract> = {
      customerId: contract.customerId!,
      startingDate: contract.startingDate!,
      endingDate: contract.endingDate!,
      totalAmount: calculation.totalAmount,
      description: contract.description || '',
      jobDetails: contract.jobDetails || [],
      status: contract.status || 0
    };

    // Ensure no address field is present
    delete (contractToSubmit as any).address;

    // Mark submission time and key to prevent rapid resubmission and duplicates
    localStorage.setItem('lastContractSubmission', now.toString());
    localStorage.setItem('lastContractSubmissionKey', submissionKey);
    setLoading(true);

    try {
      console.log('🚀 Submitting contract creation request...');
      console.log('📋 Contract object keys:', Object.keys(contractToSubmit));
      console.log('📋 Contract object:', JSON.stringify(contractToSubmit, null, 2));

      // Check if address field exists
      if ('address' in contractToSubmit) {
        console.warn('⚠️ WARNING: address field found in contract object!', contractToSubmit.address);
      } else {
        console.log('✅ No address field in contract object');
      }

      // Clear any previous error state
      setError(null);

      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);
      console.log('✅ Contract created successfully:', {
        id: createdContract.id,
        totalAmount: createdContract.totalAmount,
        customerId: createdContract.customerId
      });

      // Verify the contract was actually created with valid data
      if (!createdContract || !createdContract.id) {
        throw new Error('Hợp đồng được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');
      }

      setSuccess(`Hợp đồng #${createdContract.id} đã được tạo thành công với tổng giá trị ${createdContract.totalAmount?.toLocaleString('vi-VN')} VNĐ!`);

      // Clear the submission timestamp and key on success
      localStorage.removeItem('lastContractSubmission');
      localStorage.removeItem('lastContractSubmissionKey');

      // Set flag to trigger refresh in contracts list
      localStorage.setItem('contractsListNeedsRefresh', 'true');

      // Redirect to the contracts list page after a short delay
      setTimeout(() => {
        navigate('/contracts');
      }, 2000);
    } catch (err: any) {
      console.error('❌ Contract creation failed:', err);

      // Provide more specific error messages
      let errorMessage = 'Đã xảy ra lỗi khi tạo hợp đồng';

      if (err.response?.status === 400) {
        errorMessage = 'Dữ liệu hợp đồng không hợp lệ. Vui lòng kiểm tra lại thông tin.';
      } else if (err.response?.status === 500) {
        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      // Clear the submission timestamp on error to allow retry
      localStorage.removeItem('lastContractSubmission');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner fullScreen />;
  }

  return (
    <Box>
      {error && <ErrorAlert message={error} />}
      {success && <SuccessAlert message={success} />}

      {/* Debug Panel - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ mb: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>Debug Info</Typography>
          <Typography variant="body2">
            Customer ID: {contract.customerId || 'Not selected'}<br/>
            Job Details: {contract.jobDetails?.length || 0}<br/>
            Total Amount: {contract.totalAmount || 0}<br/>
            Has Work Shifts: {contract.jobDetails?.some(jd => jd.workShifts?.length > 0) ? 'Yes' : 'No'}
          </Typography>
          <Box sx={{ mt: 2 }}>
            <button
              onClick={() => {
                console.log('🔍 Current contract state:', contract);
                console.log('🔍 Validation result:', validateContract());
              }}
              style={{ marginRight: '10px', padding: '5px 10px' }}
            >
              Log Contract State
            </button>
            <button
              onClick={async () => {
                console.log('🚀 Testing direct API call...');
                try {
                  const testContract = {
                    customerId: 1,
                    startingDate: "2024-12-20",
                    endingDate: "2024-12-31",
                    totalAmount: 5000000,
                    description: "Test Description",
                    jobDetails: [
                      {
                        jobCategoryId: 1,
                        startDate: "2024-12-20",
                        endDate: "2024-12-31",
                        workLocation: "Test Location",
                        workShifts: [
                          {
                            startTime: "08:00",
                            endTime: "17:00",
                            numberOfWorkers: 3,
                            salary: 500000,
                            workingDays: "1,2,3,4,5"
                          }
                        ]
                      }
                    ]
                  };
                  const result = await contractService.createContract(testContract as any);
                  console.log('✅ Direct API test successful:', result);
                  alert('Direct API test successful! Check console for details.');
                } catch (error) {
                  console.error('❌ Direct API test failed:', error);
                  alert('Direct API test failed! Check console for details.');
                }
              }}
              style={{ padding: '5px 10px', marginRight: '10px' }}
            >
              Test Direct API
            </button>
            <button
              onClick={() => {
                console.log('🧹 Clearing localStorage...');
                localStorage.removeItem('lastContractSubmission');
                localStorage.removeItem('lastContractSubmissionKey');
                alert('LocalStorage cleared! You can now try submitting again.');
              }}
              style={{ padding: '5px 10px', backgroundColor: '#ff9800', color: 'white' }}
            >
              Clear LocalStorage
            </button>
          </Box>
        </Box>
      )}

      <CustomerContractForm
        contract={contract}
        onChange={handleContractChange}
        onSubmit={handleSubmit}
        loading={loading}
      />
    </Box>
  );
};

export default CreateContractPage;
