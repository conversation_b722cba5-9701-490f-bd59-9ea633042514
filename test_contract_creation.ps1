# Test script để kiểm tra tạo hợp đồng qua API
# Chạy script này để test backend trư<PERSON><PERSON> khi kiểm tra frontend

Write-Host "=== TESTING CONTRACT CREATION API ===" -ForegroundColor Green

# Test data - hợp đồng đơn giản với đầy đủ thông tin bắt buộc
$contractData = @{
    customerId = 1
    startingDate = "2024-12-20"
    endingDate = "2024-12-31"
    totalAmount = 5000000
    address = "123 Test Address"
    description = "Test contract description"
    jobDetails = @(
        @{
            jobCategoryId = 1
            startDate = "2024-12-20"
            endDate = "2024-12-31"
            workLocation = "Test Work Location"
            workShifts = @(
                @{
                    startTime = "08:00"
                    endTime = "17:00"
                    numberOfWorkers = 3
                    salary = 500000
                    workingDays = "1,2,3,4,5"
                }
            )
        }
    )
} | ConvertTo-Json -Depth 5

Write-Host "Contract Data:" -ForegroundColor Yellow
Write-Host $contractData

# Test URLs
$apiGatewayUrl = "http://localhost:8080/api/customer-contract"
$directServiceUrl = "http://localhost:8083/api/contracts"

function Test-ContractAPI {
    param(
        [string]$TestName,
        [string]$Url,
        [string]$Data
    )
    
    Write-Host "`n=== Testing $TestName ===" -ForegroundColor Cyan
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $headers = @{
            'Content-Type' = 'application/json'
        }
        
        $response = Invoke-RestMethod -Uri $Url -Method POST -Body $Data -Headers $headers -ErrorAction Stop
        
        Write-Host "✅ SUCCESS: Contract created successfully!" -ForegroundColor Green
        Write-Host "Contract ID: $($response.id)" -ForegroundColor Green
        Write-Host "Customer ID: $($response.customerId)" -ForegroundColor Green
        Write-Host "Total Amount: $($response.totalAmount)" -ForegroundColor Green
        Write-Host "Status: $($response.status)" -ForegroundColor Green
        
        if ($response.jobDetails -and $response.jobDetails.Count -gt 0) {
            Write-Host "Job Details Count: $($response.jobDetails.Count)" -ForegroundColor Green
            foreach ($jobDetail in $response.jobDetails) {
                Write-Host "  - Job Category ID: $($jobDetail.jobCategoryId)" -ForegroundColor Gray
                Write-Host "  - Work Location: $($jobDetail.workLocation)" -ForegroundColor Gray
                if ($jobDetail.workShifts -and $jobDetail.workShifts.Count -gt 0) {
                    Write-Host "  - Work Shifts Count: $($jobDetail.workShifts.Count)" -ForegroundColor Gray
                    foreach ($shift in $jobDetail.workShifts) {
                        Write-Host "    * Time: $($shift.startTime) - $($shift.endTime)" -ForegroundColor Gray
                        Write-Host "    * Workers: $($shift.numberOfWorkers)" -ForegroundColor Gray
                        Write-Host "    * Salary: $($shift.salary)" -ForegroundColor Gray
                        Write-Host "    * Working Days: $($shift.workingDays)" -ForegroundColor Gray
                    }
                }
            }
        }
        
        return $true
    }
    catch {
        Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "Status Code: $statusCode" -ForegroundColor Red
            
            try {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorBody = $reader.ReadToEnd()
                Write-Host "Error Body: $errorBody" -ForegroundColor Red
            }
            catch {
                Write-Host "Could not read error response body" -ForegroundColor Red
            }
        }
        
        return $false
    }
}

# Test 1: Qua API Gateway
$gatewaySuccess = Test-ContractAPI -TestName "API Gateway" -Url $apiGatewayUrl -Data $contractData

# Test 2: Trực tiếp đến service
$directSuccess = Test-ContractAPI -TestName "Direct Service" -Url $directServiceUrl -Data $contractData

# Kết quả tổng kết
Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Magenta
if ($gatewaySuccess) {
    Write-Host "✅ API Gateway: PASSED" -ForegroundColor Green
} else {
    Write-Host "❌ API Gateway: FAILED" -ForegroundColor Red
}

if ($directSuccess) {
    Write-Host "✅ Direct Service: PASSED" -ForegroundColor Green
} else {
    Write-Host "❌ Direct Service: FAILED" -ForegroundColor Red
}

if ($gatewaySuccess -or $directSuccess) {
    Write-Host "`n🎉 Backend API is working! The issue might be in the frontend." -ForegroundColor Green
} else {
    Write-Host "`n🚨 Backend API has issues. Need to check backend services." -ForegroundColor Red
}

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. If backend works: Check frontend request format" -ForegroundColor White
Write-Host "2. If backend fails: Check service logs and database connection" -ForegroundColor White
Write-Host "3. Verify all microservices are running (customer-service, job-service, customer-contract-service)" -ForegroundColor White
